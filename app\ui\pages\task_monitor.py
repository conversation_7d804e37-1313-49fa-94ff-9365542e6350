import streamlit as st
import asyncio
import time
from datetime import datetime
from typing import Dict, Any, List
from ..workflows.workflow_utils import api_request, get_auth_headers
from ..components.progress_monitor import show_real_time_progress_monitor


def show_task_monitor_page():
    """显示任务监控页面"""
    st.title("📊 任务监控")
    st.markdown("实时监控所有任务的执行状态和进度")
    
    # 控制面板
    col1, col2, col3 = st.columns([1, 1, 2])
    
    with col1:
        auto_refresh = st.checkbox("🔄 自动刷新", value=True, key="task_monitor_auto_refresh")
    
    with col2:
        refresh_interval = st.selectbox(
            "刷新间隔",
            options=[3, 5, 10, 30],
            index=1,
            format_func=lambda x: f"{x}秒",
            key="refresh_interval"
        )
    
    with col3:
        if st.button("🔄 立即刷新", use_container_width=True):
            st.rerun()
    
    st.markdown("---")
    
    # 任务统计
    stats_container = st.empty()
    
    # 任务列表容器
    tasks_container = st.empty()
    
    # 获取并显示任务
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        tasks = loop.run_until_complete(
            api_request("GET", "/status", headers=get_auth_headers())
        )
        loop.close()
        
        if tasks and isinstance(tasks, list):
            # 显示统计信息
            show_task_statistics(tasks, stats_container)
            
            # 显示任务列表
            show_task_list(tasks, tasks_container)
            
        else:
            tasks_container.info("📝 暂无任务")
            
    except Exception as e:
        tasks_container.error(f"❌ 获取任务列表失败: {str(e)}")
    
    # 自动刷新逻辑
    if auto_refresh:
        time.sleep(refresh_interval)
        st.rerun()


def show_task_statistics(tasks: List[Dict[str, Any]], container):
    """显示任务统计信息"""
    # 统计各状态的任务数量
    stats = {
        "pending": 0,
        "running": 0,
        "completed": 0,
        "failed": 0
    }
    
    for task in tasks:
        status = task.get("status", "unknown")
        if status in stats:
            stats[status] += 1
    
    with container.container():
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("⏳ 等待中", stats["pending"])
        
        with col2:
            st.metric("🔵 运行中", stats["running"], delta=f"+{stats['running']}")
        
        with col3:
            st.metric("✅ 已完成", stats["completed"], delta=f"+{stats['completed']}")
        
        with col4:
            st.metric("❌ 失败", stats["failed"], delta=f"+{stats['failed']}" if stats['failed'] > 0 else None)


def show_task_list(tasks: List[Dict[str, Any]], container):
    """显示任务列表"""
    with container.container():
        # 按状态分组显示
        running_tasks = [t for t in tasks if t.get("status") == "running"]
        pending_tasks = [t for t in tasks if t.get("status") == "pending"]
        completed_tasks = [t for t in tasks if t.get("status") == "completed"]
        failed_tasks = [t for t in tasks if t.get("status") == "failed"]
        
        # 显示运行中的任务（优先级最高）
        if running_tasks:
            st.markdown("### 🔵 运行中的任务")
            for task in running_tasks:
                show_task_card(task, expanded=True, show_progress=True)
        
        # 显示等待中的任务
        if pending_tasks:
            st.markdown("### ⏳ 等待中的任务")
            for task in pending_tasks:
                show_task_card(task, expanded=False, show_progress=False)
        
        # 显示最近完成的任务（最多5个）
        if completed_tasks:
            st.markdown("### ✅ 最近完成的任务")
            for task in completed_tasks[:5]:
                show_task_card(task, expanded=False, show_progress=False)
        
        # 显示失败的任务
        if failed_tasks:
            st.markdown("### ❌ 失败的任务")
            for task in failed_tasks:
                show_task_card(task, expanded=False, show_progress=False)


def show_task_card(task: Dict[str, Any], expanded: bool = False, show_progress: bool = False):
    """显示单个任务卡片"""
    task_id = task.get("task_id", "")
    status = task.get("status", "unknown")
    workflow_key = task.get("workflow_key", "")
    created_at = task.get("created_at", "")
    
    # 状态图标和颜色
    status_config = {
        "pending": {"icon": "🟡", "color": "orange"},
        "running": {"icon": "🔵", "color": "blue"}, 
        "completed": {"icon": "🟢", "color": "green"},
        "failed": {"icon": "🔴", "color": "red"}
    }
    
    config = status_config.get(status, {"icon": "⚪", "color": "gray"})
    
    with st.expander(f"{config['icon']} {workflow_key} - {task_id[:8]}", expanded=expanded):
        # 基本信息
        col1, col2 = st.columns([3, 1])
        
        with col1:
            st.markdown(f"**任务ID**: `{task_id}`")
            st.markdown(f"**工作流**: {workflow_key}")
            st.markdown(f"**状态**: :{config['color']}[{status.upper()}]")
            st.markdown(f"**创建时间**: {created_at}")
            
            # 显示错误信息
            error_message = task.get("error_message")
            if error_message:
                st.error(f"错误: {error_message}")
        
        with col2:
            if status == "running" and show_progress:
                if st.button("📊 详细进度", key=f"detail_progress_{task_id}"):
                    show_real_time_progress_monitor(task_id, f"任务 {task_id[:8]} 详细进度")
        
        # 显示进度信息
        if show_progress:
            show_task_progress_summary(task)


def show_task_progress_summary(task: Dict[str, Any]):
    """显示任务进度摘要"""
    workflow_progress = task.get("workflow_progress")
    batch_progress = task.get("batch_progress")
    
    if workflow_progress:
        st.markdown("**🔧 工作流进度**")
        overall_progress = workflow_progress.get("overall_progress", 0.0)
        current_node = workflow_progress.get("current_node")
        
        st.progress(overall_progress / 100.0)
        if current_node:
            st.caption(f"当前节点: {current_node}")
    
    if batch_progress:
        st.markdown("**📁 批量处理进度**")
        current = batch_progress.get("current_index", 0)
        total = batch_progress.get("total_count", 0)
        current_file = batch_progress.get("current_filename", "")
        
        if total > 0:
            batch_percent = (current / total) * 100
            st.progress(batch_percent / 100.0)
            st.caption(f"进度: {current}/{total} - {current_file}")
            
            # 显示完成和失败统计
            completed = len(batch_progress.get("completed_files", []))
            failed = len(batch_progress.get("failed_files", []))
            
            col1, col2 = st.columns(2)
            with col1:
                st.metric("已完成", completed)
            with col2:
                st.metric("失败", failed)


def show_task_monitor_sidebar():
    """显示任务监控侧边栏"""
    with st.sidebar:
        st.markdown("### 🎛️ 监控控制")
        
        # 快速操作
        if st.button("🔄 刷新所有任务", use_container_width=True):
            st.rerun()
        
        if st.button("🧹 清理完成任务", use_container_width=True):
            try:
                # 这里可以添加清理已完成任务的API调用
                st.success("清理完成")
            except Exception as e:
                st.error(f"清理失败: {str(e)}")
        
        st.markdown("---")
        
        # 监控设置
        st.markdown("### ⚙️ 监控设置")
        
        show_details = st.checkbox("显示详细信息", value=True)
        show_logs = st.checkbox("显示日志", value=False)
        
        if show_logs:
            st.markdown("### 📝 系统日志")
            st.text_area("日志输出", value="暂无日志", height=200, disabled=True)
