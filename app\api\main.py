from fastapi import FastAPI, HTTPException, Depends, status, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from typing import List, Dict, Any, Optional
import os
from datetime import datetime, timedelta
import uuid
import asyncio
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

from .schemas import *
from .auth import auth_manager, get_current_user, get_admin_user, get_current_user_optional
from .comfy_client import comfy_client

# 创建 FastAPI 应用
app = FastAPI(
    title="FreemanWorkHub API",
    description="ComfyUI 工作流管理与自动化平台 API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制为特定域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 内存中的任务存储（生产环境应使用数据库）
tasks_store: Dict[str, TaskInfo] = {}

# 启动时初始化
@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化"""
    print("FreemanWorkHub API 启动中...")
    
    # 创建必要的目录
    data_dir = os.getenv("DATA_DIR", "app/data")
    for subdir in ["inputs", "outputs", "temp"]:
        os.makedirs(f"{data_dir}/{subdir}", exist_ok=True)
    
    # 检查 ComfyUI 连接
    try:
        is_connected = await comfy_client.check_connection()
        if is_connected:
            print(f"✓ ComfyUI 连接成功: {comfy_client.base_url}")
        else:
            print(f"⚠ ComfyUI 连接失败: {comfy_client.base_url}")
    except Exception as e:
        print(f"⚠ ComfyUI 连接检查出错: {str(e)}")
    
    print("FreemanWorkHub API 启动完成！")

# 健康检查端点（无需认证）
@app.get("/health", response_model=ApiResponse)
async def health_check():
    """健康检查"""
    comfy_status = await comfy_client.check_connection()
    
    return ApiResponse(
        success=True,
        message="服务运行正常",
        data={
            "timestamp": datetime.now().isoformat(),
            "comfyui_connected": comfy_status,
            "auth_enabled": auth_manager.auth_enabled
        }
    )

# 认证相关端点
@app.post("/auth/login", response_model=LoginResponse)
async def login(request: LoginRequest):
    """用户登录"""
    user_info = auth_manager.verify_access_key(request.username, request.access_key)
    
    if not user_info:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或访问密钥无效"
        )
    
    token = auth_manager.create_token(user_info)
    expires_at = datetime.now() + timedelta(minutes=auth_manager.session_ttl_min)
    
    return LoginResponse(
        token=token,
        expires_at=expires_at,
        user=user_info
    )

@app.get("/auth/me", response_model=UserInfo)
async def get_current_user_info(current_user: UserInfo = Depends(get_current_user)):
    """获取当前用户信息"""
    return current_user

# 占位的扫码登录端点
@app.post("/auth/qr/init")
async def init_qr_login(current_user: UserInfo = Depends(get_current_user_optional)):
    """初始化扫码登录 - 占位实现"""
    return auth_manager.init_qr_login()

@app.get("/auth/qr/status")
async def check_qr_status(login_id: str, current_user: UserInfo = Depends(get_current_user_optional)):
    """检查扫码登录状态 - 占位实现"""
    return auth_manager.check_qr_status(login_id)

@app.post("/auth/qr/redeem")
async def redeem_qr_code(login_id: Optional[str] = None, current_user: UserInfo = Depends(get_current_user_optional)):
    """兑换扫码登录结果 - 占位实现"""
    return auth_manager.redeem_qr_code(login_id)

@app.post("/auth/bind")
async def bind_account(current_user: UserInfo = Depends(get_current_user)):
    """账号绑定 - 占位实现"""
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="账号绑定功能尚未实现"
    )

# 工作流管理端点
@app.get("/workflows", response_model=List[WorkflowInfo])
async def list_workflows(current_user: UserInfo = Depends(get_current_user)):
    """获取工作流列表"""
    from ..services.workflow_service import workflow_service
    
    # 根据用户角色过滤工作流
    all_workflows = await workflow_service.list_workflows_async()
    
    if "admin" in current_user.roles:
        # 管理员可以看到所有工作流
        return all_workflows
    else:
        # 普通用户只能看到 RDY 标签的工作流
        return [w for w in all_workflows if WorkflowTag.RDY in w.metadata.tags]

@app.get("/workflows/grouped", response_model=Dict[str, List[WorkflowInfo]])
async def list_workflows_grouped(current_user: UserInfo = Depends(get_current_user)):
    """获取按目录分组的工作流列表"""
    from ..services.workflow_service import workflow_service
    
    # 获取按目录分组的工作流
    grouped_workflows = await workflow_service.get_workflows_grouped_by_directory()
    
    # 根据用户角色过滤工作流
    filtered_grouped = {}
    for directory, workflows in grouped_workflows.items():
        if "admin" in current_user.roles:
            # 管理员可以看到所有工作流
            filtered_workflows = workflows
        else:
            # 普通用户只能看到 RDY 标签的工作流
            filtered_workflows = [w for w in workflows if WorkflowTag.RDY in w.metadata.tags]
        
        if filtered_workflows:  # 只包含有工作流的目录
            filtered_grouped[directory] = filtered_workflows
    
    return filtered_grouped

@app.get("/workflows/{workflow_key}")
async def get_workflow(
    workflow_key: str,
    force_reload: bool = False,
    current_user: UserInfo = Depends(get_current_user)
):
    """获取指定工作流详情

    Args:
        workflow_key: 工作流键
        force_reload: 是否强制从磁盘重新读取，用于同步ComfyUI的修改
    """
    from ..services.workflow_service import workflow_service

    workflow = await workflow_service.get_workflow_async(workflow_key, force_reload=force_reload)
    if not workflow:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"工作流 {workflow_key} 不存在"
        )

    # 检查权限
    if WorkflowTag.DEV in workflow.metadata.tags and "admin" not in current_user.roles:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问此工作流"
        )

    return {
        "success": True,
        "message": "工作流获取成功",
        "data": workflow
    }

@app.get("/workflows/{workflow_key}/debug", response_model=Dict[str, Any])
async def debug_workflow(workflow_key: str, current_user: UserInfo = Depends(get_current_user)):
    """调试工作流详细信息"""
    from ..services.workflow_service import workflow_service
    from ..ui.workflows.workflow_utils import extract_notes_from_workflow, analyze_workflow_nodes
    
    workflow = await workflow_service.get_workflow_async(workflow_key)
    if not workflow:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"工作流 {workflow_key} 不存在"
        )
    
    # 检查权限
    if WorkflowTag.DEV in workflow.metadata.tags and "admin" not in current_user.roles:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问此工作流"
        )
    
    workflow_json = workflow.workflow_json
    
    # 提取简介
    notes_content = extract_notes_from_workflow(workflow_json)
    
    # 分析节点
    nodes_analysis = analyze_workflow_nodes(workflow_json)
    
    # 统计信息
    node_types = {}
    for node_id, node_data in workflow_json.items():
        if isinstance(node_data, dict):
            class_type = node_data.get("class_type", "unknown")
            node_types[class_type] = node_types.get(class_type, 0) + 1
    
    return {
        "workflow_key": workflow_key,
        "file_path": workflow.file_path,
        "nodes_count": len(workflow_json),
        "node_types": node_types,
        "notes_content": notes_content,
        "editable_nodes_count": len(nodes_analysis["editable_nodes"]),
        "editable_nodes": nodes_analysis["editable_nodes"],
        "model_nodes_count": len(nodes_analysis["model_nodes"]),
        "model_nodes": nodes_analysis["model_nodes"],
        "sample_nodes": {k: v for k, v in list(workflow_json.items())[:3]}  # 显示前3个节点作为样本
    }

@app.post("/workflows/{workflow_key}/preview")
async def preview_workflow(
    workflow_key: str,
    request: Dict[str, Any],
    current_user: UserInfo = Depends(get_current_user)
):
    """根据传入的参数覆盖，返回更新后的工作流 JSON（不执行）。"""
    from ..services.workflow_service import workflow_service

    workflow = await workflow_service.get_workflow_async(workflow_key)
    if not workflow:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"工作流 {workflow_key} 不存在"
        )

    # 权限校验：非管理员不可预览 DEV 工作流
    if WorkflowTag.DEV in workflow.metadata.tags and "admin" not in current_user.roles:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问此工作流"
        )

    base_workflow = workflow.workflow_json or {}
    parameters = (request or {}).get("parameters", {})
    try:
        updated = comfy_client.update_workflow_parameters(base_workflow, parameters)
        return {
            "workflow_key": workflow_key,
            "parameters_count": len(parameters or {}),
            "updated_workflow": updated
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"预览失败: {str(e)}")

@app.post("/workflows/{workflow_key}/save")
async def save_workflow(
    workflow_key: str,
    request: Dict[str, Any],
    current_user: UserInfo = Depends(get_current_user)
):
    """保存更新后的工作流 JSON 到原始文件，并让 ComfyUI 端即可感知变更。
    - 支持两种请求体：
      1) {"parameters": {"<path>": value, ...}} 按路径覆盖现有 workflow_json 后保存
      2) {"workflow_json": {...}} 直接保存完整 JSON
    - 返回保存结果与文件信息。
    权限：DEV 标签仅管理员可保存。
    """
    from ..services.workflow_service import workflow_service

    workflow = await workflow_service.get_workflow_async(workflow_key)
    if not workflow:
        raise HTTPException(status_code=404, detail=f"工作流 {workflow_key} 不存在")

    if WorkflowTag.DEV in workflow.metadata.tags and "admin" not in current_user.roles:
        raise HTTPException(status_code=403, detail="没有权限保存此工作流")

    parameters = (request or {}).get("parameters")
    updated_workflow_json = (request or {}).get("workflow_json")

    try:
        result = await workflow_service.save_workflow(
            workflow_key,
            parameters=parameters,
            updated_workflow_json=updated_workflow_json,
            make_backup=True,
        )
        # ComfyUI 侧通常在读取本地文件时不会自动拉取；但多数用户在 ComfyUI 前端层会热加载文件变更。
        # 这里直接返回成功与文件元数据。若需要，可在后续引入文件变更通知机制。
        return ApiResponse(success=True, message="保存成功", data=result)
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/workflows/sync", response_model=ApiResponse)
async def sync_workflows(request: Dict[str, str], current_user: UserInfo = Depends(get_admin_user)):
    """从 ComfyUI 目录同步工作流（仅管理员）"""
    from ..services.workflow_service import workflow_service
    
    workflow_dir = request.get("workflow_dir")
    if not workflow_dir:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="必须提供 workflow_dir 参数"
        )
    
    result = await workflow_service.sync_from_comfyui_directory(workflow_dir)
    
    if result.get("success"):
        message = result.get("message", "同步完成")
    else:
        message = f"同步失败：{result.get('error', '未知错误')}"
    
    return ApiResponse(
        success=result.get("success", False),
        message=message,
        data=result
    )

# 任务执行端点
@app.post("/run", response_model=TaskInfo)
async def run_workflow(
    request: RunWorkflowRequest,
    background_tasks: BackgroundTasks,
    current_user: UserInfo = Depends(get_current_user)
):
    """执行工作流"""
    from ..services.workflow_service import workflow_service

    # 如果请求中包含工作流目录，先设置目录
    if request.workflow_dir:
        workflow_service.set_comfyui_workflow_dir(request.workflow_dir)

    # 验证工作流存在且有权限
    workflow = await workflow_service.get_workflow_async(request.workflow_key)
    if not workflow:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"工作流 {request.workflow_key} 不存在"
        )
    
    if WorkflowTag.DEV in workflow.metadata.tags and "admin" not in current_user.roles:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限执行此工作流"
        )
    
    # 创建任务
    task_id = str(uuid.uuid4())
    task = TaskInfo(
        task_id=task_id,
        workflow_key=request.workflow_key,
        status=TaskStatus.PENDING,
        created_at=datetime.now()
    )
    
    tasks_store[task_id] = task
    
    # 在后台执行任务
    background_tasks.add_task(execute_workflow_task, task_id, request, workflow)
    
    return task

@app.get("/status/{task_id}", response_model=TaskInfo)
async def get_task_status(task_id: str, current_user: UserInfo = Depends(get_current_user)):
    """获取任务状态"""
    if task_id not in tasks_store:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"任务 {task_id} 不存在"
        )
    
    return tasks_store[task_id]

@app.get("/status", response_model=List[TaskInfo])
async def list_tasks(current_user: UserInfo = Depends(get_current_user)):
    """获取任务列表"""
    return list(tasks_store.values())

@app.delete("/status/{task_id}")
async def cancel_task(task_id: str, current_user: UserInfo = Depends(get_current_user)):
    """取消任务"""
    if task_id not in tasks_store:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"任务 {task_id} 不存在"
        )
    
    task = tasks_store[task_id]
    if task.status == TaskStatus.RUNNING:
        # TODO: 实现 ComfyUI 任务取消
        task.status = TaskStatus.FAILED
        task.error_message = "任务已被用户取消"
        task.completed_at = datetime.now()
    
    return ApiResponse(success=True, message="任务已取消")

# 输出文件端点
@app.get("/outputs")
async def list_outputs(current_user: UserInfo = Depends(get_current_user)):
    """获取输出文件列表"""
    data_dir = os.getenv("DATA_DIR", "app/data")
    outputs_dir = f"{data_dir}/outputs"
    
    if not os.path.exists(outputs_dir):
        return ApiResponse(success=True, data=[])
    
    files = []
    for root, dirs, filenames in os.walk(outputs_dir):
        for filename in filenames:
            filepath = os.path.join(root, filename)
            relpath = os.path.relpath(filepath, outputs_dir)
            stat = os.stat(filepath)
            
            files.append({
                "path": relpath,
                "size": stat.st_size,
                "modified": datetime.fromtimestamp(stat.st_mtime).isoformat()
            })
    
    return ApiResponse(success=True, data=files)

@app.get("/outputs/{file_path:path}")
async def download_output(file_path: str, current_user: UserInfo = Depends(get_current_user)):
    """下载输出文件"""
    data_dir = os.getenv("DATA_DIR", "app/data")
    full_path = os.path.join(data_dir, "outputs", file_path)
    
    if not os.path.exists(full_path) or not os.path.isfile(full_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文件不存在"
        )
    
    return FileResponse(full_path)

# 配置管理端点
@app.get("/config")
async def get_config(current_user: UserInfo = Depends(get_admin_user)):
    """获取系统配置（仅管理员）"""
    return ApiResponse(
        success=True,
        data={
            "comfyui_base_url": comfy_client.base_url,
            "auth_enabled": auth_manager.auth_enabled,
            "auth_mode": auth_manager.auth_mode,
            "data_dir": os.getenv("DATA_DIR", "app/data")
        }
    )

# 背景任务执行函数
async def execute_workflow_task(task_id: str, request: RunWorkflowRequest, workflow: WorkflowInfo):
    """在后台执行工作流任务"""
    task = tasks_store[task_id]

    try:
        print(f"🚀 开始执行任务 {task_id}, 工作流: {request.workflow_key}")
        task.status = TaskStatus.RUNNING
        task.started_at = datetime.now()

        # 更新工作流参数
        print(f"📝 更新工作流参数: {request.parameters}")
        updated_workflow = comfy_client.update_workflow_parameters(
            workflow.workflow_json or {},
            request.parameters
        )

        # 提交到 ComfyUI
        print(f"📤 提交工作流到 ComfyUI: {comfy_client.base_url}")
        try:
            response = await comfy_client.submit_prompt(updated_workflow)
            print(f"✅ ComfyUI 返回 prompt_id: {response.prompt_id}")

            # 等待执行完成
            prompt_id = response.prompt_id
            task.progress = {"prompt_id": prompt_id, "status": "submitted"}
            print(f"📋 任务进度更新: {task.progress}")
        except Exception as submit_error:
            print(f"❌ 提交到ComfyUI失败: {submit_error}")
            raise submit_error
        
        # 轮询执行状态
        max_retries = 60  # 最多等待 5 分钟
        for retry in range(max_retries):
            await asyncio.sleep(5)
            
            history = await comfy_client.get_history(prompt_id)
            if history:
                history_item = history[0]
                task.progress = {"prompt_id": prompt_id, "history": history_item.dict()}
                
                if history_item.outputs:
                    # 任务完成
                    task.status = TaskStatus.COMPLETED
                    task.completed_at = datetime.now()
                    task.result = {"outputs": history_item.outputs}
                    break
        else:
            # 超时
            task.status = TaskStatus.FAILED
            task.completed_at = datetime.now()
            task.error_message = "任务执行超时"
    
    except Exception as e:
        task.status = TaskStatus.FAILED
        task.completed_at = datetime.now()
        task.error_message = str(e)

if __name__ == "__main__":
    import uvicorn
    
    port = int(os.getenv("API_PORT", "8001"))
    uvicorn.run(app, host="0.0.0.0", port=port, reload=True)
